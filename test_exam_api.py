#!/usr/bin/env python3
"""
Test script để kiểm tra exam generation API
"""
import requests
import json

def test_exam_generation():
    """Test exam generation API"""
    url = "http://localhost:8001/api/v1/exam/generate-exam-test"
    
    # Test payload với 60 câu hỏi
    payload = {
        "exam_id": "hoa12_de_60cau",
        "ten_truong": "Trường THPT Nguyễn <PERSON>",
        "mon_hoc": "Hóa học",
        "lop": 12,
        "tong_so_cau": 60,
        "cau_hinh_de": [
            {
                "lesson_id": "234",
                "yeu_cau_can_dat": "Hiểu và phân biệt proton, neutron, electron theo khối lư<PERSON>ng, điện tích và vị trí.",
                "muc_do": [
                    { "loai": "Nhận biết", "so_cau": 10, "loai_cau": ["TN"] },
                    { "loai": "Thông hiểu", "so_cau": 5, "loai_cau": ["TN"] },
                    { "loai": "Vận dụng", "so_cau": 5, "loai_cau": ["TN"] }
                ]
            },
            {
                "lesson_id": "235",
                "yeu_cau_can_dat": "Viết và xác định cấu hình electron các nguyên tố từ Z = 1 đến Z = 30.",
                "muc_do": [
                    { "loai": "Nhận biết", "so_cau": 5, "loai_cau": ["TN"] },
                    { "loai": "Thông hiểu", "so_cau": 10, "loai_cau": ["TN"] },
                    { "loai": "Vận dụng", "so_cau": 5, "loai_cau": ["TN"] }
                ]
            },
            {
                "lesson_id": "236",
                "yeu_cau_can_dat": "Tính nguyên tử khối trung bình từ dữ liệu đồng vị.",
                "muc_do": [
                    { "loai": "Nhận biết", "so_cau": 5, "loai_cau": ["TN"] },
                    { "loai": "Thông hiểu", "so_cau": 10, "loai_cau": ["TN"] },
                    { "loai": "Vận dụng", "so_cau": 5, "loai_cau": ["TN"] }
                ]
            }
        ]
    }
    
    try:
        print("Testing exam generation API...")
        print(f"URL: {url}")
        print("Payload created successfully")

        response = requests.post(url, json=payload, timeout=60)

        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            print("API hoat dong binh thuong!")
            print("File DOCX da duoc tao thanh cong!")

            # Kiểm tra headers để xem thông tin về số câu hỏi
            headers = response.headers
            total_questions = headers.get('X-Total-Questions', 'Unknown')
            test_mode = headers.get('X-Test-Mode', 'false')

            print(f"Total questions generated: {total_questions}")
            print(f"Test mode: {test_mode}")
            print(f"Content-Type: {headers.get('Content-Type', 'Unknown')}")
            print(f"File size: {len(response.content)} bytes")

            return True
        else:
            print(f"API tra ve loi: {response.status_code}")
            try:
                print("Response:", response.json())
            except:
                print("Response text:", response.text[:500])
            return False

    except Exception as e:
        print(f"Loi khi test API: {e}")
        return False

if __name__ == "__main__":
    test_exam_generation()
