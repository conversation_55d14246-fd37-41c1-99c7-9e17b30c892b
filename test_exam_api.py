#!/usr/bin/env python3
"""
Test script để kiểm tra exam generation API
"""
import requests
import json

def test_exam_generation():
    """Test exam generation API"""
    url = "http://localhost:8000/api/v1/exam/generate-exam-test"
    
    # Test payload đơn giản
    payload = {
        "exam_id": "test_exam_001",
        "mon_hoc": "H<PERSON>a học",
        "lop": 12,
        "tong_so_cau": 5,
        "ten_truong": "Test School",
        "cau_hinh_de": [
            {
                "lesson_id": "lesson_01_01",
                "yeu_cau_can_dat": "Hiểu về cấu tạo nguyên tử",
                "muc_do": [
                    {
                        "loai": "Nhận biết",
                        "so_cau": 2,
                        "loai_cau": ["TN"]
                    },
                    {
                        "loai": "Thông hiểu",
                        "so_cau": 2,
                        "loai_cau": ["TN"]
                    },
                    {
                        "loai": "<PERSON>ận dụng",
                        "so_cau": 1,
                        "loai_cau": ["TN"]
                    }
                ]
            }
        ]
    }
    
    try:
        print("Testing exam generation API...")
        print(f"URL: {url}")
        print("Payload created successfully")

        response = requests.post(url, json=payload, timeout=60)

        print(f"Status Code: {response.status_code}")
        print(f"Response: {response.text}")

        if response.status_code == 200:
            print("API hoat dong binh thuong!")
            return True
        else:
            print(f"API tra ve loi: {response.status_code}")
            return False

    except Exception as e:
        print(f"Loi khi test API: {e}")
        return False

if __name__ == "__main__":
    test_exam_generation()
