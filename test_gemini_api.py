#!/usr/bin/env python3
"""
Test script để kiểm tra Gemini API key
"""
import os
import google.generativeai as genai
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_gemini_api():
    """Test Gemini API với key hiện tại"""
    try:
        # Lấy API key từ .env
        api_key = os.getenv("GEMINI_API_KEY")
        print(f"API Key: {api_key[:10]}...{api_key[-10:] if api_key else 'None'}")
        
        if not api_key:
            print("GEMINI_API_KEY khong duoc set trong .env file")
            return False

        # Configure Gemini
        genai.configure(api_key=api_key)

        # Tạo model
        model = genai.GenerativeModel('models/gemini-1.5-flash-latest')
        print("Model duoc tao thanh cong")
        
        # Test với một prompt đơn giản
        print("Testing voi prompt don gian...")
        response = model.generate_content("Viết 1 câu hỏi trắc nghiệm về hóa học đơn giản")

        print("API hoat dong binh thuong!")
        print(f"Response: {response.text[:200]}...")
        return True

    except Exception as e:
        print(f"Loi khi test Gemini API: {e}")
        print(f"Error type: {type(e).__name__}")

        # Kiểm tra các loại lỗi phổ biến
        error_str = str(e).lower()
        if "quota" in error_str or "resource_exhausted" in error_str:
            print("Nguyen nhan: API quota da het")
        elif "api key" in error_str or "authentication" in error_str:
            print("Nguyen nhan: API key khong hop le")
        elif "permission" in error_str:
            print("Nguyen nhan: API key khong co quyen truy cap")
        else:
            print("Nguyen nhan: Loi khac")
        
        return False

if __name__ == "__main__":
    print("Kiem tra Gemini API Key...")
    test_gemini_api()
